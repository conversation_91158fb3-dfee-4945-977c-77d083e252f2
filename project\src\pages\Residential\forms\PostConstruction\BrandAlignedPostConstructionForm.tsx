import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, CheckCircle, ArrowRight, ChevronLeft,
  Building2, Warehouse, Wind, Sun, Paintbrush2, 
  HardHat, Brush, Trash2
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { useAuth } from '../../../../lib/auth/AuthProvider';

interface FormData {
  propertyType: string;
  propertySize: string;
  cleaningServices: string[];
  constructionType: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredStartDate: string;
  cleaningTimeline: string;
  specialInstructions: string;
  howDidYouHear: string;
  newsletter: boolean;
  serviceType: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const BrandAlignedPostConstructionForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FormData>>({
    propertyType: '',
    propertySize: '',
    cleaningServices: [],
    constructionType: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredStartDate: '',
    cleaningTimeline: '',
    specialInstructions: '',
    howDidYouHear: '',
    newsletter: false,
    serviceType: 'post-construction-cleaning'
  });
  
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Save form data to localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('postConstructionCleaningFormData');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setFormData(parsed);
        localStorage.removeItem('postConstructionCleaningFormData');
      } catch (error) {
        console.error('Error parsing saved form data:', error);
      }
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('postConstructionCleaningFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Property Details' },
    { id: 2, name: 'Service Schedule' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact & Schedule' },
  ];

  // Property types for post-construction cleaning
  const propertyTypes = [
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: Building2, 
      description: 'Studio to 3BR units'
    },
    { 
      id: 'house', 
      name: 'Single Family House', 
      icon: Home, 
      description: '1-2 story homes'
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: Warehouse, 
      description: 'Multi-level attached'
    },
    { 
      id: 'large-home', 
      name: 'Large Home/Estate', 
      icon: HardHat, 
      description: '3+ stories, 4+ bedrooms'
    }
  ];

  // Property sizes for cleaning estimates
  const propertySizes = [
    { id: 'small', name: 'Small (< 1,200 sq ft)', description: 'Starting at $85', estimateRange: '$85 - $120' },
    { id: 'medium', name: 'Medium (1,200 - 2,000 sq ft)', description: 'Starting at $120', estimateRange: '$120 - $180' },
    { id: 'large', name: 'Large (2,000 - 3,000 sq ft)', description: 'Starting at $180', estimateRange: '$180 - $250' },
    { id: 'xl', name: 'Extra Large (3,000+ sq ft)', description: 'Starting at $250', estimateRange: '$250+' }
  ];

  // Construction types that need cleaning
  const constructionTypes = [
    {
      id: 'renovation',
      name: 'Home Renovation',
      description: 'Kitchen, bathroom, or room remodel',
      icon: Paintbrush2,
      popular: true
    },
    {
      id: 'new-construction',
      name: 'New Construction',
      description: 'Brand new home or addition',
      icon: HardHat,
      popular: true
    },
    {
      id: 'flooring',
      name: 'Flooring Installation',
      description: 'Hardwood, tile, carpet installation',
      icon: Home,
      popular: true
    },
    {
      id: 'painting',
      name: 'Painting Project',
      description: 'Interior or exterior painting',
      icon: Brush
    },
    {
      id: 'roofing',
      name: 'Roofing Work',
      description: 'Roof replacement or repair',
      icon: Home
    },
    {
      id: 'other',
      name: 'Other Construction',
      description: 'Electrical, plumbing, HVAC work',
      icon: Wind
    }
  ];

  // Post-construction cleaning services
  const cleaningServices = [
    {
      id: 'dust-removal',
      name: 'Dust & Debris Removal',
      description: 'Complete dust removal from all surfaces',
      icon: Wind,
      popular: true
    },
    {
      id: 'window-cleaning',
      name: 'Window Cleaning',
      description: 'Interior and exterior window cleaning',
      icon: Sparkles,
      popular: true
    },
    {
      id: 'floor-cleaning',
      name: 'Floor Deep Cleaning',
      description: 'Deep cleaning of all floor surfaces',
      icon: Home,
      popular: true
    },
    {
      id: 'paint-removal',
      name: 'Paint Splatter Removal',
      description: 'Remove paint drops and splatters',
      icon: Paintbrush2,
      popular: true
    },
    {
      id: 'fixture-cleaning',
      name: 'Fixture & Appliance Cleaning',
      description: 'Clean all fixtures, appliances, and hardware',
      icon: Sun,
      popular: true
    },
    {
      id: 'cabinet-cleaning',
      name: 'Cabinet Interior Cleaning',
      description: 'Clean inside cabinets and drawers',
      icon: Building2
    },
    {
      id: 'baseboard-cleaning',
      name: 'Baseboard & Trim Cleaning',
      description: 'Detailed cleaning of baseboards and trim',
      icon: Brush
    },
    {
      id: 'debris-removal',
      name: 'Construction Debris Removal',
      description: 'Remove leftover construction materials',
      icon: Trash2
    }
  ];

  const timelineOptions = [
    { id: 'asap', name: 'As Soon As Possible' },
    { id: '1-week', name: 'Within 1 Week' },
    { id: '2-weeks', name: 'Within 2 Weeks' },
    { id: 'flexible', name: 'Flexible Timeline' }
  ];

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName':
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      case 'email':
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      case 'phone':
        return !validatePhone(value) ? 'Please enter a valid 10-digit phone number' : '';
      case 'address':
        return value.length < 5 ? 'Please enter a complete address' : '';
      case 'city':
        return value.length < 2 ? 'Please enter a valid city' : '';
      case 'zipCode':
        return !validateZipCode(value) ? 'Please enter a valid ZIP code (12345 or 12345-6789)' : '';
      default:
        return '';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Real-time validation
    const error = validateField(field, value);
    setValidationErrors(prev => ({
      ...prev,
      [field]: error
    }));
  };

  // Step validation
  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.propertySize);
      case 2:
        return !!(formData.constructionType && formData.preferredStartDate && formData.cleaningTimeline);
      case 3:
        return !!(formData.cleaningServices && formData.cleaningServices.length > 0);
      case 4: {
        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode'];
        return requiredFields.every(field => {
          const value = formData[field as keyof FormData] as string;
          return value && !validateField(field, value);
        });
      }
      default:
        return false;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!isStepValid(4)) return;
    
    // Check for user login
    if (!user) {
      console.log("User not authenticated. Redirecting to login.");
      localStorage.setItem('postConstructionCleaningFormData', JSON.stringify(formData));
      navigate('/auth/login', { state: { from: '/residential/postconstruction' }});
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Save to localStorage for persistence
      localStorage.setItem('postConstructionBookingData', JSON.stringify({
        ...formData,
        serviceType: 'post-construction-cleaning',
        serviceDisplayName: 'Post-Construction Cleaning',
        submittedAt: new Date().toISOString()
      }));
      
      // Navigate to thank you page
      navigate('/thank-you', { 
        state: { 
          formData: formData,
          message: 'Thank you for booking our post-construction cleaning service! We will contact you within 24 hours to confirm your appointment.'
        }
      });
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleServiceToggle = (serviceId: string) => {
    const currentServices = formData.cleaningServices || [];
    if (currentServices.includes(serviceId)) {
      setFormData({
        ...formData,
        cleaningServices: currentServices.filter(id => id !== serviceId)
      });
    } else {
      setFormData({
        ...formData,
        cleaningServices: [...currentServices, serviceId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-6xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <HardHat className="w-10 h-10 text-green-400" />
              </motion.div>
              <h1 className="text-3xl sm:text-4xl font-bold text-white">
                Post-Construction Cleaning
              </h1>
            </div>
            <p className="text-gray-200">Professional cleaning service after your renovation or construction project is complete.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= step.id ? 'bg-green-500 text-white' : 'bg-white/20 text-gray-400'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-white' : 'text-gray-400'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-white/20 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-green-500 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Form */}
            <div className="lg:col-span-2">
              <motion.div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 sm:p-8" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
                <AnimatePresence mode="wait">
                  {/* Step 1: Property Details */}
                  {currentStep === 1 && (
                    <motion.div key="step1" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-white mb-6">Tell us about your property</h2>
                      
                      {/* Property Type Selection */}
                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-white mb-4">Property Type</label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          {propertyTypes.map((type) => {
                            const IconComponent = type.icon;
                            return (
                              <motion.button
                                key={type.id}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => setFormData({ ...formData, propertyType: type.id, propertySize: '' })}
                                className={`relative p-4 rounded-2xl border-2 transition-all text-center ${
                                  formData.propertyType === type.id
                                    ? 'bg-green-500/20 border-green-400/30 shadow-md'
                                    : 'bg-white/5 border-white/20 hover:border-white/30'
                                }`}
                              >
                                <div className={`${
                                  formData.propertyType === type.id ? 'text-green-400' : 'text-white/80'
                                } mb-2 flex justify-center`}>
                                  <IconComponent className="w-6 h-6" />
                                </div>
                                <h3 className="font-semibold text-white text-sm">{type.name}</h3>
                                <p className="text-xs text-white/80 mt-1">{type.description}</p>
                              </motion.button>
                            );
                          })}
                        </div>
                      </div>

                      {/* Property Size Selection */}
                      {formData.propertyType && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-8"
                        >
                          <label className="block text-sm font-semibold text-white mb-4">Property Size</label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {propertySizes.map((size) => (
                              <motion.button
                                key={size.id}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => setFormData({ ...formData, propertySize: size.id })}
                                className={`relative p-6 rounded-2xl border-2 transition-all text-left ${
                                  formData.propertySize === size.id
                                    ? 'bg-green-500/20 border-green-400/30 shadow-md'
                                    : 'bg-white/5 border-white/20 hover:border-white/30'
                                }`}
                              >
                                <div className="flex items-start gap-4">
                                  <div className={`${
                                    formData.propertySize === size.id ? 'text-green-400' : 'text-white/80'
                                  }`}>
                                    <Home className="w-8 h-8" />
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-start justify-between mb-2">
                                      <h3 className="font-semibold text-white text-lg">{size.name}</h3>
                                      <span className="text-lg font-bold text-green-400">{size.estimateRange}</span>
                                    </div>
                                    <p className="text-sm text-white/80 mb-1">{size.description}</p>
                                    <p className="text-xs text-white/60">Estimated price range</p>
                                  </div>
                                </div>
                              </motion.button>
                            ))}
                          </div>
                        </motion.div>
                      )}

                      <div className="flex justify-end">
                        <Button 
                          onClick={() => setCurrentStep(2)} 
                          disabled={!isStepValid(1)}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Continue <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {/* Step 2: Service Schedule */}
                  {currentStep === 2 && (
                    <motion.div key="step2" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-white mb-6">What type of construction was completed?</h2>
                      
                      {/* Construction Type */}
                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-white mb-4">Construction/Renovation Type</label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          {constructionTypes.map((type) => {
                            const IconComponent = type.icon;
                            return (
                              <motion.button
                                key={type.id}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => setFormData({ ...formData, constructionType: type.id })}
                                className={`relative p-4 rounded-2xl border-2 transition-all text-center ${
                                  formData.constructionType === type.id
                                    ? 'bg-green-500/20 border-green-400/30 shadow-md'
                                    : 'bg-white/5 border-white/20 hover:border-white/30'
                                }`}
                              >
                                <div className={`${
                                  formData.constructionType === type.id ? 'text-green-400' : 'text-white/80'
                                } mb-2 flex justify-center`}>
                                  <IconComponent className="w-6 h-6" />
                                </div>
                                <h3 className="font-semibold text-white text-sm">{type.name}</h3>
                                <p className="text-xs text-white/80 mt-1">{type.description}</p>
                                {type.popular && (
                                  <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                                    Popular
                                  </div>
                                )}
                              </motion.button>
                            );
                          })}
                        </div>
                      </div>

                      {/* Scheduling Section */}
                      <div className="mb-8 p-6 bg-white/5 rounded-2xl border border-white/10">
                        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                          <Sun className="w-5 h-5 text-green-400" />
                          Schedule Your Cleaning
                        </h3>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {/* Preferred Start Date */}
                          <div>
                            <label className="block text-sm font-semibold text-white mb-3">Preferred Start Date *</label>
                            <input
                              type="date"
                              value={formData.preferredStartDate || ''}
                              onChange={(e) => handleInputChange('preferredStartDate', e.target.value)}
                              className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              min={new Date().toISOString().split('T')[0]}
                            />
                          </div>

                          {/* Timeline */}
                          <div>
                            <label className="block text-sm font-semibold text-white mb-3">Timeline *</label>
                            <GlassmorphismSelect
                              value={formData.cleaningTimeline || ''}
                              onChange={(value) => setFormData({ ...formData, cleaningTimeline: value })}
                              options={timelineOptions.map(option => ({ id: option.id, name: option.name }))}
                              placeholder="Select timeline"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Special Instructions */}
                      <div className="mb-8">
                        <label className="block text-sm font-semibold text-white mb-3">
                          Special Instructions
                          <span className="text-white/60 font-normal ml-2">(Optional)</span>
                        </label>
                        <textarea
                          value={formData.specialInstructions || ''}
                          onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                          placeholder="Any specific areas that need extra attention, access instructions, or special requirements..."
                          className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                          rows={4}
                        />
                        <p className="text-xs text-white/50 mt-2">
                          💡 Include details about dust-heavy areas, delicate surfaces, or access requirements
                        </p>
                      </div>

                      <div className="flex justify-between">
                        <Button 
                          onClick={() => setCurrentStep(1)} 
                          className="bg-white/10 hover:bg-white/20 text-white border border-white/20 font-semibold rounded-xl px-6 py-3 transition-all duration-300"
                        >
                          <ChevronLeft className="mr-2 h-5 w-5" /> Back
                        </Button>
                        <Button 
                          onClick={() => setCurrentStep(3)} 
                          disabled={!isStepValid(2)}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Continue <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {/* Step 3: Add-ons */}
                  {currentStep === 3 && (
                    <motion.div key="step3" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-white mb-6">Select cleaning services needed</h2>
                      <p className="text-gray-300 mb-6">Choose the post-construction cleaning services you need</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                        {cleaningServices.map((service) => {
                          const IconComponent = service.icon;
                          const isSelected = formData.cleaningServices?.includes(service.id);
                          
                          return (
                            <motion.button
                              key={service.id}
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => handleServiceToggle(service.id)}
                              className={`relative p-6 rounded-2xl border-2 transition-all text-left ${
                                isSelected
                                  ? 'bg-green-500/20 border-green-400/30 shadow-md'
                                  : 'bg-white/5 border-white/20 hover:border-white/30'
                              }`}
                            >
                              <div className="flex items-start gap-4">
                                <div className={`${
                                  isSelected ? 'text-green-400' : 'text-white/80'
                                }`}>
                                  <IconComponent className="w-8 h-8" />
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-start justify-between mb-2">
                                    <h3 className="font-semibold text-white text-lg">{service.name}</h3>
                                    {isSelected && (
                                      <CheckCircle className="w-6 h-6 text-green-400" />
                                    )}
                                  </div>
                                  <p className="text-sm text-white/80">{service.description}</p>
                                </div>
                              </div>
                              {service.popular && (
                                <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                                  Popular
                                </div>
                              )}
                            </motion.button>
                          );
                        })}
                      </div>

                      <div className="flex justify-between">
                        <Button 
                          onClick={() => setCurrentStep(2)} 
                          className="bg-white/10 hover:bg-white/20 text-white border border-white/20 font-semibold rounded-xl px-6 py-3 transition-all duration-300"
                        >
                          <ChevronLeft className="mr-2 h-5 w-5" /> Back
                        </Button>
                        <Button 
                          onClick={() => setCurrentStep(4)} 
                          disabled={!isStepValid(3)}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Continue <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {/* Step 4: Contact Information */}
                  {currentStep === 4 && (
                    <motion.div key="step4" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-white mb-6">Contact Information</h2>
                      <p className="text-gray-300 mb-6">
                        We'll review your post-construction cleaning requirements and contact you within 24 hours with a detailed quote.
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">First Name *</label>
                          <input
                            type="text"
                            value={formData.firstName || ''}
                            onChange={(e) => handleInputChange('firstName', e.target.value)}
                            className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            placeholder="Enter first name"
                          />
                          {validationErrors.firstName && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.firstName}</p>
                          )}
                        </div>
                        
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">Last Name *</label>
                          <input
                            type="text"
                            value={formData.lastName || ''}
                            onChange={(e) => handleInputChange('lastName', e.target.value)}
                            className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            placeholder="Enter last name"
                          />
                          {validationErrors.lastName && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.lastName}</p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">Email *</label>
                          <input
                            type="email"
                            value={formData.email || ''}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            placeholder="Enter email address"
                          />
                          {validationErrors.email && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.email}</p>
                          )}
                        </div>
                        
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">Phone *</label>
                          <input
                            type="tel"
                            value={formData.phone || ''}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            placeholder="Enter phone number"
                          />
                          {validationErrors.phone && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.phone}</p>
                          )}
                        </div>
                      </div>

                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-white mb-3">Address *</label>
                        <input
                          type="text"
                          value={formData.address || ''}
                          onChange={(e) => handleInputChange('address', e.target.value)}
                          className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder="Enter street address"
                        />
                        {validationErrors.address && (
                          <p className="text-red-400 text-sm mt-1">{validationErrors.address}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">City *</label>
                          <input
                            type="text"
                            value={formData.city || ''}
                            onChange={(e) => handleInputChange('city', e.target.value)}
                            className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            placeholder="Enter city"
                          />
                          {validationErrors.city && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.city}</p>
                          )}
                        </div>
                        
                        <div>
                          <label className="block text-sm font-semibold text-white mb-3">ZIP Code *</label>
                          <input
                            type="text"
                            value={formData.zipCode || ''}
                            onChange={(e) => handleInputChange('zipCode', e.target.value)}
                            className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            placeholder="Enter ZIP code"
                          />
                          {validationErrors.zipCode && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.zipCode}</p>
                          )}
                        </div>
                      </div>

                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-white mb-3">How did you hear about us?</label>
                        <GlassmorphismSelect
                          value={formData.howDidYouHear || ''}
                          onChange={(value) => setFormData({ ...formData, howDidYouHear: value })}
                          options={[
                            { id: 'google', name: 'Google Search' },
                            { id: 'social', name: 'Social Media' },
                            { id: 'referral', name: 'Friend/Family Referral' },
                            { id: 'contractor', name: 'Contractor Recommendation' },
                            { id: 'other', name: 'Other' }
                          ]}
                          placeholder="Select option"
                        />
                      </div>

                      <div className="mb-8">
                        <label className="flex items-center gap-3 text-white cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.newsletter || false}
                            onChange={(e) => setFormData({ ...formData, newsletter: e.target.checked })}
                            className="w-5 h-5 text-green-500 bg-white/10 border-white/20 rounded focus:ring-green-500"
                          />
                          <span className="text-sm">Subscribe to our newsletter for cleaning tips and special offers</span>
                        </label>
                      </div>

                      <div className="flex justify-between">
                        <Button 
                          onClick={() => setCurrentStep(3)} 
                          className="bg-white/10 hover:bg-white/20 text-white border border-white/20 font-semibold rounded-xl px-6 py-3 transition-all duration-300"
                        >
                          <ChevronLeft className="mr-2 h-5 w-5" /> Back
                        </Button>
                        <Button 
                          onClick={handleSubmit}
                          disabled={!isStepValid(4) || isSubmitting}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-8 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isSubmitting ? 'Submitting...' : 'Get Quote'}
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <motion.div 
                className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 sticky top-8"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                  <Sparkles className="w-6 h-6 text-green-400" />
                  Why Choose Our Post-Construction Cleaning?
                </h3>
                
                <div className="space-y-4 text-sm text-white/80">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Specialized equipment for construction dust removal</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Experienced team trained in post-construction cleanup</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Safe removal of paint splatters and debris</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Detailed cleaning of all surfaces and fixtures</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Licensed, bonded, and insured professionals</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>100% satisfaction guarantee</span>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-green-500/10 rounded-xl border border-green-400/30">
                  <h4 className="font-semibold text-white mb-2">💡 Pro Tip</h4>
                  <p className="text-sm text-white/80">
                    Schedule your post-construction cleaning as soon as your project is complete to prevent dust and debris from settling deeper into surfaces.
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedPostConstructionForm; 